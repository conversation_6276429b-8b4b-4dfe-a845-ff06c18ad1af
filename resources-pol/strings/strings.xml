<strings>
    <string id="AppName">Segment34 MkII</string>

    <string id="UNIT_KCAL">KCAL</string>
    <string id="UNIT_M">M</string>
    <string id="UNIT_FT">FT</string>
    <string id="UNIT_STEPS">KROKI</string>
    <string id="UNIT_PUSHES">PCHNIĘCIA</string>
    <string id="LABEL_FL">ODCZ</string>
    <string id="LABEL_NA">N/D</string>
    <string id="LABEL_POS_NA">POZYCJA N/D</string>

    <string id="WEATHER_0">BEZCHMURNIE</string>
    <string id="WEATHER_1">CZĘŚCIOWO POCHMURNO</string>
    <string id="WEATHER_2">PRZEWAŻNIE POCHMURNO</string>
    <string id="WEATHER_3">DESZCZ</string>
    <string id="WEATHER_4">ŚNIEG</string>
    <string id="WEATHER_5">WIETRZNIE</string>
    <string id="WEATHER_6">BURZE</string>
    <string id="WEATHER_7">MIESZANKA ZIMOWA</string>
    <string id="WEATHER_8">MGŁA</string>
    <string id="WEATHER_9">MGIEŁKA</string>
    <string id="WEATHER_10">GRAD</string>
    <string id="WEATHER_11">PRZELOTNE OPADY</string>
    <string id="WEATHER_12">PRZEL. BURZE</string>
    <string id="WEATHER_13">NIEZNANE OPADY</string>
    <string id="WEATHER_14">LEKKI DESZCZ</string>
    <string id="WEATHER_15">ULEWA</string>
    <string id="WEATHER_16">LEKKI ŚNIEG</string>
    <string id="WEATHER_17">OBFITY ŚNIEG</string>
    <string id="WEATHER_18">LEKKI DESZCZ ZE ŚNIEGIEM</string>
    <string id="WEATHER_19">OBFITY DESZCZ ZE ŚNIEGIEM</string>
    <string id="WEATHER_20">POCHMURNO</string>
    <string id="WEATHER_21">DESZCZ ZE ŚNIEGIEM</string>
    <string id="WEATHER_22">CZĘŚCIOWO BEZCHMURNIE</string>
    <string id="WEATHER_23">PRZEWAŻNIE BEZCHMURNIE</string>
    <string id="WEATHER_24">LEKKIE OPADY</string>
    <string id="WEATHER_25">OPADY</string>
    <string id="WEATHER_26">SILNE OPADY</string>
    <string id="WEATHER_27">MOŻLIWE OPADY</string>
    <string id="WEATHER_28">MOŻLIWE BURZE</string>
    <string id="WEATHER_29">ZAMGŁENIE</string>
    <string id="WEATHER_30">KURZ</string>
    <string id="WEATHER_31">MŻAWKA</string>
    <string id="WEATHER_32">TORNADO</string>
    <string id="WEATHER_33">DYM</string>
    <string id="WEATHER_34">LOD</string>
    <string id="WEATHER_35">PIASEK</string>
    <string id="WEATHER_36">SZKWAŁ</string>
    <string id="WEATHER_37">BURZA PIASKOWA</string>
    <string id="WEATHER_38">POPIOŁ WULKANICZNY</string>
    <string id="WEATHER_39">MGŁA</string>
    <string id="WEATHER_40">POGODNIE</string>
    <string id="WEATHER_41">HURAGAN</string>
    <string id="WEATHER_42">BURZA TROPIKALNA</string>
    <string id="WEATHER_43">MOŻLIWY ŚNIEG</string>
    <string id="WEATHER_44">DESZCZ/ŚNIEG?</string>
    <string id="WEATHER_45">POCHMURNO, DESZCZ?</string>
    <string id="WEATHER_46">POCHMURNO, MOŻLIWY ŚNIEG</string>
    <string id="WEATHER_47">POCHMURNO, DESZCZ/ŚNIEG?</string>
    <string id="WEATHER_48">ZAMIECIE</string>
    <string id="WEATHER_49">MARZNĄCY DESZCZ</string>
    <string id="WEATHER_50">DESZCZ ZE ŚNIEGIEM</string>
    <string id="WEATHER_51">LÓD I ŚNIEG</string>
    <string id="WEATHER_52">CIENKIE CHMURY</string>
    <string id="WEATHER_53">NIEZNANE</string>

    <string id="DAY_OF_WEEK_SUN">NIE</string>
    <string id="DAY_OF_WEEK_MON">PON</string>
    <string id="DAY_OF_WEEK_TUE">WTO</string>
    <string id="DAY_OF_WEEK_WED">ŚRO</string>
    <string id="DAY_OF_WEEK_THU">CZW</string>
    <string id="DAY_OF_WEEK_FRI">PIĄ</string>
    <string id="DAY_OF_WEEK_SAT">SOB</string>

    <string id="MONTH_JAN">STY</string>
    <string id="MONTH_FEB">LUT</string>
    <string id="MONTH_MAR">MAR</string>
    <string id="MONTH_APR">KWI</string>
    <string id="MONTH_MAY">MAJ</string>
    <string id="MONTH_JUN">CZE</string>
    <string id="MONTH_JUL">LIP</string>
    <string id="MONTH_AUG">SIE</string>
    <string id="MONTH_SEP">WRZ</string>
    <string id="MONTH_OCT">PAŹ</string>
    <string id="MONTH_NOV">LIS</string>
    <string id="MONTH_DEC">GRU</string>

    <string id="LABEL_WMIN_1">MIN TG</string>
    <string id="LABEL_WMIN_2">MIN TYG</string>
    <string id="LABEL_WMIN_3">AKT. MIN TYG</string>
    <string id="LABEL_DMIN_1">MIN DN</string>
    <string id="LABEL_DMIN_2">MIN DZIŚ</string>
    <string id="LABEL_DMIN_3">AKT. MIN DZIEŃ</string>
    <string id="LABEL_DKM_1">KM DN</string>
    <string id="LABEL_DKM_2">KM DZIŚ</string>
    <string id="LABEL_DMI_1">MI DN</string>
    <string id="LABEL_DMI_2">MI DZIŚ</string>
    <string id="LABEL_DMI_3">MIL DZIŚ</string>
    <string id="LABEL_FLOORS">PIĘTRA:</string>
    <string id="LABEL_CLIMB_1">WSPIN</string>
    <string id="LABEL_CLIMB_2">M WSPIN</string>
    <string id="LABEL_RECOV_1">REKOW</string>
    <string id="LABEL_RECOV_2">GODZ. REK</string>
    <string id="LABEL_RECOV_3">GODZ. REKOW</string>
    <string id="LABEL_VO2_1">VO2</string>
    <string id="LABEL_VO2_2">VO2 MAX</string>
    <string id="LABEL_VO2RUN_3">BIEG VO2 MAX</string>
    <string id="LABEL_VO2BIKE_3">ROWER VO2 MAX</string>
    <string id="LABEL_RESP_1">ODDECH</string>
    <string id="LABEL_RESP_2">CZĘST. ODDECHU</string>
    <string id="LABEL_RESP_3">CZĘST. ODDECHU</string>
    <string id="LABEL_HR">TĘTNO:</string>
    <string id="LABEL_CAL_1">KAL</string>
    <string id="LABEL_CAL_2">KALORIE</string>
    <string id="LABEL_CAL_3">KALORIE DZIŚ</string>
    <string id="LABEL_ALT_1">WYS</string>
    <string id="LABEL_ALT_2">WYSOKOŚĆ</string>
    <string id="LABEL_ALTM_3">WYSOKOŚĆ M</string>
    <string id="LABEL_STRESS">STRES:</string>
    <string id="LABEL_ALTFT_3">WYSOKOŚĆ FT</string>
    <string id="LABEL_BBAT_1">B BATT</string>
    <string id="LABEL_BBAT_2">BATT CIAŁA</string>
    <string id="LABEL_BBAT_3">BATERIA CIAŁA</string>
    <string id="LABEL_STEPS">KROKI:</string>
    <string id="LABEL_DIST_1">DYST</string>
    <string id="LABEL_DIST_2">M DZIŚ</string>
    <string id="LABEL_DIST_3">METRÓW DZIŚ</string>
    <string id="LABEL_PUSHES">PCHNIĘCIA:</string>
    <string id="LABEL_WKM_1">KM TYG</string>
    <string id="LABEL_WRUNM_2">BIEG KM TG</string>
    <string id="LABEL_WRUNM_3">BIEG KM TYG</string>
    <string id="LABEL_WMI_1">MI TYG</string>
    <string id="LABEL_WRUNMI_2">BIEG MI TG</string>
    <string id="LABEL_WRUNMI_3">BIEG MI TYG</string>
    <string id="LABEL_WBIKEKM_2">ROWER KM</string>
    <string id="LABEL_WBIKEKM_3">ROWER KM TYG</string>
    <string id="LABEL_WBIKEMI_2">ROWER MI</string>
    <string id="LABEL_WBIKEMI_3">ROWER MI TYG</string>
    <string id="LABEL_TRAINING">TRENING:</string>
    <string id="LABEL_PRESSURE">CIŚNIENIE:</string>
    <string id="LABEL_KG_1">KG</string>
    <string id="LABEL_WEIGHT_2">WAGA</string>
    <string id="LABEL_KG_3">WAGA KG</string>
    <string id="LABEL_LBS_1">FUNTY</string>
    <string id="LABEL_LBS_3">WAGA FUNTY</string>
    <string id="LABEL_ACAL_1">A KAL</string>
    <string id="LABEL_ACAL_2">AKT. KAL</string>
    <string id="LABEL_ACAL_3">AKT. KALORIE</string>
    <string id="LABEL_WEEK">TYDZIEŃ:</string>
    <string id="LABEL_WDISTKM_2">KM TYG</string>
    <string id="LABEL_WDISTKM_3">DYST. KM TYG</string>
    <string id="LABEL_WDISTMI_2">MI TYG</string>
    <string id="LABEL_WDISTMI_3">MIL TYG</string>
    <string id="LABEL_BATT_1">BATT</string>
    <string id="LABEL_BATT_2">BATT %</string>
    <string id="LABEL_BATT_3">BATERIA %</string>
    <string id="LABEL_BATTD_1">BATT D</string>
    <string id="LABEL_BATTD_2">BATT DNI</string>
    <string id="LABEL_BATTD_3">BATERIA DNI</string>
    <string id="LABEL_NOTIFS_1">POW</string>
    <string id="LABEL_NOTIFS_3">POWIADOMIENIA</string>
    <string id="LABEL_SUN_1">SŁOŃCE</string>
    <string id="LABEL_SUNINT_2">INT SŁON</string>
    <string id="LABEL_SUNINT_3">INT. SŁOŃCA</string>
    <string id="LABEL_TEMP_1">TEMP</string>
    <string id="LABEL_STEMP_3">TEMP. CZUJNIKA</string>
    <string id="LABEL_DAWN_1">WSCH</string>
    <string id="LABEL_DAWN_2">WSCHÓD</string>
    <string id="LABEL_DUSK_1">ZACH</string>
    <string id="LABEL_DUSK_2">ZACHÓD</string>
    <string id="LABEL_ALARM_1">ALARM</string>
    <string id="LABEL_ALARM_2">ALARMY</string>
    <string id="LABEL_HIGH_1">WYSOKA</string>
    <string id="LABEL_HIGH_2">DZIENNA WYSOKA</string>
    <string id="LABEL_LOW_1">NISKA</string>
    <string id="LABEL_LOW_2">DZIENNA NISKA</string>
    <string id="LABEL_TEMP_3">TEMPERATURA</string>
    <string id="LABEL_PRECIP_1">OPADY</string>
    <string id="LABEL_PRECIP_3">OPADY</string>
    <string id="LABEL_NEXTSUN_1">SŁOŃCE</string>
    <string id="LABEL_NEXTSUN_2">NAST SŁON</string>
    <string id="LABEL_NEXTSUN_3">NAST. SŁOŃCE</string>
    <string id="LABEL_NEXTCAL_1">KAL</string>
    <string id="LABEL_NEXTCAL_2">NAST. KAL</string>
    <string id="LABEL_NEXTCAL_3">NAST. KAL</string>
    <string id="LABEL_OX_1">TLEN</string>
    <string id="LABEL_OX_2">PULS TLEN</string>
    <string id="LABEL_ACC_1">DOKŁ</string>
    <string id="LABEL_ACC_2">DOKŁ. POZ</string>
    <string id="LABEL_ACC_3">DOKŁ. POZYCJI</string>
    <string id="LABEL_UV_1">UV</string>
    <string id="LABEL_UV_2">INDEKS UV</string>
    <string id="LABEL_HUM_1">WILG</string>
    <string id="LABEL_HUM_2">WILG</string>

    <string id="settings_color_theme" scope="settings">Motyw kolorystyczny</string>
    <string id="settings_yellow_on_turquoise" scope="settings">Żółty na turkusowym</string>
    <string id="settings_yellow_on_blue" scope="settings">Żółty na niebieskim</string>
    <string id="settings_hot_pink" scope="settings">Gorący róż</string>
    <string id="settings_blue" scope="settings">Niebieski</string>
    <string id="settings_blueish_green" scope="settings">Niebieskozielony</string>
    <string id="settings_green" scope="settings">Zielony</string>
    <string id="settings_green_camo" scope="settings">Zielony kamuflaż</string>
    <string id="settings_green_and_orange" scope="settings">Zielony i pomarańczowy</string>
    <string id="settings_orange" scope="settings">Pomarańczowy</string>
    <string id="settings_amber" scope="settings">Bursztynowy</string>
    <string id="settings_peachy_orange" scope="settings">Brzoskwiniowy pomarańczowy</string>
    <string id="settings_red" scope="settings">Czerwony</string>
    <string id="settings_purple" scope="settings">Fioletowy</string>
    <string id="settings_white_on_turquoise" scope="settings">Biały na turkusowym</string>
    <string id="settings_white_on_red" scope="settings">Biały na czerwonym</string>
    <string id="settings_white_on_blue" scope="settings">Biały na niebieskim</string>
    <string id="settings_white_on_orange" scope="settings">Biały na pomarańczowym</string>
    <string id="settings_white_on_black" scope="settings">Biały na czarnym</string>
    <string id="settings_black_on_white" scope="settings">Czarny na białym (nie zalecane dla AMOLED)</string>
    <string id="settings_red_on_white" scope="settings">Czerwony na białym (nie zalecane dla AMOLED)</string>
    <string id="settings_blue_on_white" scope="settings">Niebieski na białym (nie zalecane dla AMOLED)</string>
    <string id="settings_green_on_white" scope="settings">Zielony na białym (nie zalecane dla AMOLED)</string>
    <string id="settings_orange_on_white" scope="settings">Pomarańczowy na białym (nie zalecane dla AMOLED)</string>
    <string id="settings_purple_on_white" scope="settings">Fioletowy na białym (nie zalecane dla AMOLED)</string>
    <string id="settings_custom_colors" scope="settings">Niestandardowe kolory, zobacz FAQ 1.2.0</string>
    <string id="settings_custom_colors_faq" scope="settings">Niestandardowe kolory, zobacz FAQ</string>
    <string id="settings_night_color_theme" scope="settings">Motyw kolorystyczny nocny</string>
    <string id="settings_no_change" scope="settings">Bez zmian</string>
    <string id="settings_night_theme_activation" scope="settings">Aktywuj nocny motyw kolorystyczny:</string>
    <string id="settings_during_scheduled_sleep" scope="settings">Podczas zaplanowanych godzin snu</string>
    <string id="settings_two_hours_before" scope="settings">2 godziny przed zaplanowanymi godzinami snu</string>
    <string id="settings_from_sunset_sunrise" scope="settings">Od zachodu do wschodu słońca (wymaga danych pogodowych)</string>
    <string id="settings_custom_colors_read" scope="settings">Niestandardowe kolory, przeczytaj FAQ</string>
    <string id="settings_enter_13_values" scope="settings">Wprowadź 13 wartości oddzielonych spacjami, format #FFFFFF lub -</string>
    <string id="settings_clock_outline_style" scope="settings">Styl obramowania zegara (tylko AMOLED)</string>
    <string id="settings_filled_numbers_no_outline" scope="settings">Wypełnione cyfry bez obramowania, to samo w AOD</string>
    <string id="settings_filled_numbers_no_outline_aod" scope="settings">Wypełnione cyfry bez obramowania, tylko obramowanie w AOD</string>
    <string id="settings_filled_numbers_no_outline_filled" scope="settings">Wypełnione cyfry bez obramowania, wypełnione kolorem obramowania w AOD</string>
    <string id="settings_filled_numbers_with_outline" scope="settings">Wypełnione cyfry z obramowaniem, to samo w AOD</string>
    <string id="settings_filled_numbers_with_outline_aod" scope="settings">Wypełnione cyfry z obramowaniem, tylko obramowanie w AOD</string>
    <string id="settings_filled_numbers_plain" scope="settings">Wypełnione cyfry bez obramowania, bez gradientu w AOD (lepsze dla redshift)</string>
    <string id="settings_update_frequency" scope="settings">Częstotliwość aktualizacji danych</string>
    <string id="settings_every_minute" scope="settings">Co minutę</string>
    <string id="settings_every_5_seconds" scope="settings">Co 5 sekund</string>
    <string id="settings_every_second" scope="settings">Co sekundę</string>
    <string id="settings_top_part_shows" scope="settings">Górna część pokazuje</string>
    <string id="settings_two_small_data" scope="settings">Dwa małe pola danych i faza księżyca</string>
    <string id="settings_two_small_data_fields" scope="settings">Dwa małe pola danych</string>
    <string id="settings_histogram_graph" scope="settings">Wykres histogramu (ostatnie 2 godziny danych)</string>
    <string id="settings_top_left_small" scope="settings">Małe pole danych górny lewy:</string>
    <string id="settings_hidden" scope="settings">Ukryte</string>
    <string id="settings_active_min_week" scope="settings">Aktywne min / tydzień</string>
    <string id="settings_active_min_day" scope="settings">Aktywne min / dzień</string>
    <string id="settings_distance_km_day" scope="settings">Dystans (km) / dzień</string>
    <string id="settings_distance_miles_day" scope="settings">Dystans (mile) / dzień</string>
    <string id="settings_run_distance_km_week" scope="settings">Dystans biegu (km) / tydzień</string>
    <string id="settings_run_distance_miles_week" scope="settings">Dystans biegu (mile) / tydzień</string>
    <string id="settings_bike_distance_km_week" scope="settings">Dystans roweru (km) / tydzień</string>
    <string id="settings_bike_distance_miles_week" scope="settings">Dystans roweru (mile) / tydzień</string>
    <string id="settings_total_distance_7_days_km" scope="settings">Całkowity dystans ostatnie 7 dni (km)</string>
    <string id="settings_total_distance_7_days_miles" scope="settings">Całkowity dystans ostatnie 7 dni (mile)</string>
    <string id="settings_floors_climbed_day" scope="settings">Piętra wspięte / dzień</string>
    <string id="settings_meters_climbed_day" scope="settings">Metry wspięte / dzień</string>
    <string id="settings_time_to_recovery" scope="settings">Czas do regeneracji (h)</string>
    <string id="settings_vo2_max_running" scope="settings">VO2 Max Bieg</string>
    <string id="settings_vo2_max_cycling" scope="settings">VO2 Max Rower</string>
    <string id="settings_respiration_rate" scope="settings">Częstotliwość oddechu</string>
    <string id="settings_heart_rate" scope="settings">Tętno</string>
    <string id="settings_pulse_ox" scope="settings">Pulsoksymetr</string>
    <string id="settings_calories_kcal_day" scope="settings">Kalorie (kcal) / dzień</string>
    <string id="settings_active_calories_kcal_day" scope="settings">Aktywne kalorie (kcal) / dzień</string>
    <string id="settings_altitude_m" scope="settings">Wysokość (m)</string>
    <string id="settings_altitude_ft" scope="settings">Wysokość (ft)</string>
    <string id="settings_stress_score" scope="settings">Wynik stresu</string>
    <string id="settings_body_battery" scope="settings">Bateria ciała</string>
    <string id="settings_sunrise_time" scope="settings">Czas wschodu słońca</string>
    <string id="settings_sunset_time" scope="settings">Czas zachodu słońca</string>
    <string id="settings_next_sun_event" scope="settings">Czas następnego zdarzenia słonecznego</string>
    <string id="settings_alternative_timezone_1" scope="settings">Alternatywna strefa czasowa 1</string>
    <string id="settings_alternative_timezone_2" scope="settings">Alternatywna strefa czasowa 2</string>
    <string id="settings_barometric_pressure_raw" scope="settings">Ciśnienie barometryczne, surowe</string>
    <string id="settings_barometric_pressure_sea" scope="settings">Ciśnienie barometryczne, poziom morza</string>
    <string id="settings_weight_kg" scope="settings">Waga (kg)</string>
    <string id="settings_weight_lbs" scope="settings">Waga (funt)</string>
    <string id="settings_week_number" scope="settings">Numer tygodnia</string>
    <string id="settings_battery_percentage" scope="settings">Procent baterii</string>
    <string id="settings_battery_days_remaining" scope="settings">Pozostałe dni baterii</string>
    <string id="settings_notification_count" scope="settings">Liczba powiadomień</string>
    <string id="settings_solar_intensity" scope="settings">Intensywność słoneczna</string>
    <string id="settings_sensor_temperature" scope="settings">Temperatura czujnika</string>
    <string id="settings_alarms_count" scope="settings">Alarmy (liczba)</string>
    <string id="settings_weather_daily_high" scope="settings">Pogoda: Dzienna temperatura maksymalna</string>
    <string id="settings_weather_daily_low" scope="settings">Pogoda: Dzienna temperatura minimalna</string>
    <string id="settings_weather_temperature" scope="settings">Pogoda: Temperatura</string>
    <string id="settings_weather_chance_precipitation" scope="settings">Pogoda: Szansa opadów</string>
    <string id="settings_weather_humidity" scope="settings">Pogoda: Wilgotność</string>
    <string id="settings_weather_uv_index" scope="settings">Pogoda: Indeks UV</string>
    <string id="settings_next_calendar_event" scope="settings">Czas następnego wydarzenia kalendarza</string>
    <string id="settings_next_calendar_event_time" scope="settings">Czas następnego wydarzenia kalendarza</string>
    <string id="settings_top_right_small" scope="settings">Małe pole danych górny prawy:</string>
    <string id="settings_histogram_shows" scope="settings">Histogram pokazuje (musi być włączone w Górna część pokazuje)</string>
    <string id="settings_body_battery_history" scope="settings">Historia baterii ciała</string>
    <string id="settings_elevation_history" scope="settings">Historia wysokości</string>
    <string id="settings_heart_rate_history" scope="settings">Historia tętna</string>
    <string id="settings_oxygen_saturation_history" scope="settings">Historia nasycenia tlenem</string>
    <string id="settings_pressure_history" scope="settings">Historia ciśnienia</string>
    <string id="settings_stress_history" scope="settings">Historia stresu</string>
    <string id="settings_stress_rest_orange_blue" scope="settings">Stres/odpoczynek (pomarańczowy/niebieski)</string>
    <string id="settings_temperature_sensor_history" scope="settings">Historia temperatury czujnika</string>
    <string id="settings_small_font_variant" scope="settings">Wariant małej czcionki matrycy kropek:</string>
    <string id="settings_dots" scope="settings">Kropki</string>
    <string id="settings_blended" scope="settings">Zmieszane</string>
    <string id="settings_lines_highest_contrast" scope="settings">Linie (najwyższy kontrast)</string>
    <string id="settings_line_1_above_clock" scope="settings">Linia 1 nad zegarem pokazuje:</string>
    <string id="settings_date" scope="settings">Data</string>
    <string id="settings_temperature_wind_feels" scope="settings">Temperatura, Wiatr, Odczuwalna</string>
    <string id="settings_temperature_wind" scope="settings">Temperatura, Wiatr</string>
    <string id="settings_temperature_wind_humidity" scope="settings">Temperatura, Wiatr, Wilgotność</string>
    <string id="settings_temperature_wind_high_low" scope="settings">Temperatura, Wiatr, Maks/Min</string>
    <string id="settings_temperature_wind_precipitation" scope="settings">Temperatura, Wiatr, Szansa opadów</string>
    <string id="settings_temperature_wind_humidity_precipitation" scope="settings">Temperatura, Wiatr, Wilgotność, Szansa opadów</string>
    <string id="settings_temperature_humidity_high_low" scope="settings">Temperatura, Wilgotność, Maks/Min</string>
    <string id="settings_temperature_uv_high_low" scope="settings">Temperatura, Indeks UV, Maks/Min</string>
    <string id="settings_temperature_feels_high_low" scope="settings">Temperatura, Odczuwalna, Maks/Min</string>
    <string id="settings_temperature_precipitation_high_low" scope="settings">Temperatura, Szansa opadów, Maks/Min</string>
    <string id="settings_weather_conditions_precipitation" scope="settings">Warunki pogodowe, Szansa opadów</string>
    <string id="settings_weather_conditions" scope="settings">Warunki pogodowe</string>
    <string id="settings_daily_high_temperature" scope="settings">Dzienna temperatura maksymalna</string>
    <string id="settings_daily_low_temperature" scope="settings">Dzienna temperatura minimalna</string>
    <string id="settings_temperature" scope="settings">Temperatura</string>
    <string id="settings_chance_of_precipitation" scope="settings">Szansa opadów</string>
    <string id="settings_steps_day" scope="settings">Kroki / dzień</string>
    <string id="settings_wheelchair_pushes_day" scope="settings">Pchnięcia wózka / dzień</string>
    <string id="settings_calories_kcal_day_2" scope="settings">Kalorie (kcal) / dzień</string>
    <string id="settings_active_total_calories" scope="settings">Aktywne/Całkowite Kalorie (kcal) / dzień</string>
    <string id="settings_altitude_m_2" scope="settings">Wysokość (m)</string>
    <string id="settings_altitude_ft_2" scope="settings">Wysokość (ft)</string>
    <string id="settings_training_status" scope="settings">Status treningu</string>
    <string id="settings_military_date_time" scope="settings">Wojskowa grupa daty i czasu (UTC)</string>
    <string id="settings_current_location_lat_long" scope="settings">Aktualna lokalizacja (szer. dł. w stopniach dziesiętnych)</string>
    <string id="settings_current_location_military" scope="settings">Aktualna lokalizacja (Wojskowy system odniesienia siatki)</string>
    <string id="settings_current_location_accuracy" scope="settings">Dokładność aktualnej lokalizacji</string>
    <string id="settings_line_2_above_clock" scope="settings">Linia 2 nad zegarem pokazuje:</string>
    <string id="settings_weather_conditions_precipitation_chance" scope="settings">Warunki pogodowe + Szansa opadów</string>
    <string id="settings_line_below_clock" scope="settings">Linia pod zegarem pokazuje:</string>
    <string id="settings_bottom_field_layout" scope="settings">Układ pól dolnych (liczba cyfr na pole)</string>
    <string id="settings_auto_default_layout" scope="settings">Auto - Domyślny układ w zależności od rozmiaru ekranu</string>
    <string id="settings_bottom_value_1_left" scope="settings">Wartość dolna 1 (lewa):</string>
    <string id="settings_bottom_value_2" scope="settings">Wartość dolna 2:</string>
    <string id="settings_bottom_value_3" scope="settings">Wartość dolna 3:</string>
    <string id="settings_bottom_value_4" scope="settings">Wartość dolna 4 (jeśli dostępna):</string>
    <string id="settings_bottom_5_digit" scope="settings">Wartość dolna 5 cyfr:</string>
    <string id="settings_bottom_5_digit_value" scope="settings">Wartość dolna 5 cyfr:</string>
    <string id="settings_distance_meter_day" scope="settings">Dystans (metr) / dzień</string>
    <string id="settings_battery_display" scope="settings">Wyświetlanie baterii:</string>
    <string id="settings_remaining_days" scope="settings">Pozostałe dni</string>
    <string id="settings_percentage_remaining" scope="settings">Pozostały procent</string>
    <string id="settings_bar" scope="settings">Pasek</string>
    <string id="settings_left_icon_indicates" scope="settings">Lewa ikona wskazuje:</string>
    <string id="settings_alarm" scope="settings">Alarm</string>
    <string id="settings_do_not_disturb" scope="settings">Nie przeszkadzać</string>
    <string id="settings_bluetooth_on_off" scope="settings">Bluetooth (wł./wył.)</string>
    <string id="settings_bluetooth_just_when_off" scope="settings">Bluetooth (tylko gdy wyłączone)</string>
    <string id="settings_move_bar" scope="settings">Pasek ruchu</string>
    <string id="settings_right_icon_indicates" scope="settings">Prawa ikona wskazuje:</string>
    <string id="settings_always_on_below" scope="settings">Zawsze włączone (pod zegarem):</string>
    <string id="settings_second_always_on" scope="settings">Drugie pole Zawsze włączone (na prawo):</string>
    <string id="settings_text_alignment_always" scope="settings">Wyrównanie tekstu dla linii Zawsze włączone (pod zegarem):</string>
    <string id="settings_left" scope="settings">Lewe</string>
    <string id="settings_center" scope="settings">Środek</string>
    <string id="settings_text_alignment_date" scope="settings">Wyrównanie tekstu dla daty:</string>
    <string id="settings_text_alignment_bottom" scope="settings">Wyrównanie tekstu dla dolnych pól danych:</string>
    <string id="settings_right" scope="settings">Prawe</string>
    <string id="settings_left_center_5_digit" scope="settings">Lewe, ale wyśrodkuj pole 5 cyfr</string>
    <string id="settings_press_top_third" scope="settings">Naciśnij górną trzecią część aby otworzyć:</string>
    <string id="settings_nothing" scope="settings">Nic</string>
    <string id="settings_toggle_night_color" scope="settings">Przełącz nocny motyw kolorystyczny</string>
    <string id="settings_battery" scope="settings">Bateria</string>
    <string id="settings_steps" scope="settings">Kroki</string>
    <string id="settings_calories" scope="settings">Kalorie</string>
    <string id="settings_floors_climbed" scope="settings">Wspięte piętra</string>
    <string id="settings_intensity_minutes" scope="settings">Minuty intensywności</string>
    <string id="settings_date_2" scope="settings">Data</string>
    <string id="settings_weather" scope="settings">Pogoda</string>
    <string id="settings_calendar" scope="settings">Kalendarz</string>
    <string id="settings_sunrise" scope="settings">Wschód słońca</string>
    <string id="settings_altitude" scope="settings">Wysokość</string>
    <string id="settings_notifications" scope="settings">Powiadomienia</string>
    <string id="settings_heart_rate_2" scope="settings">Tętno</string>
    <string id="settings_weekly_run_distance" scope="settings">Tygodniowy dystans biegu</string>
    <string id="settings_weekly_bike_distance" scope="settings">Tygodniowy dystans roweru</string>
    <string id="settings_recovery_time" scope="settings">Czas regeneracji</string>
    <string id="settings_stress" scope="settings">Stres</string>
    <string id="settings_body_battery_2" scope="settings">Bateria ciała</string>
    <string id="settings_training_status_2" scope="settings">Status treningu</string>
    <string id="settings_race_predictor_5k" scope="settings">Predyktor wyścigu 5k</string>
    <string id="settings_pulse_ox_2" scope="settings">Pulsoksymetr</string>
    <string id="settings_solar_input" scope="settings">Dopływ słoneczny</string>
    <string id="settings_press_middle_clock" scope="settings">Naciśnij środek (zegar) aby otworzyć:</string>
    <string id="settings_press_bottom_left" scope="settings">Naciśnij dolny lewy aby otworzyć:</string>
    <string id="settings_press_bottom_center" scope="settings">Naciśnij dolny środek aby otworzyć:</string>
    <string id="settings_press_bottom_right" scope="settings">Naciśnij dolny prawy aby otworzyć:</string>
    <string id="settings_hemisphere_moon" scope="settings">Półkula (dla ikony księżyca)</string>
    <string id="settings_northern" scope="settings">Północna</string>
    <string id="settings_southern" scope="settings">Południowa</string>
    <string id="settings_time_format" scope="settings">Format czasu (12/24h)</string>
    <string id="settings_auto_use_system" scope="settings">Auto - Użyj ustawienia systemu</string>
    <string id="settings_24h" scope="settings">24h</string>
    <string id="settings_12h" scope="settings">12h</string>
    <string id="settings_date_format" scope="settings">Format daty</string>
    <string id="settings_weekday_dd_month_yyyy" scope="settings">DZIEŃ TYGODNIA, DD MIESIĄC RRRR</string>
    <string id="settings_weekday_dd_month" scope="settings">DZIEŃ TYGODNIA, DD MIESIĄC</string>
    <string id="settings_yyyy_mm_dd" scope="settings">RRRR-MM-DD</string>
    <string id="settings_mm_dd_yyyy" scope="settings">MM/DD/RRRR</string>
    <string id="settings_dd_mm_yyyy" scope="settings">DD.MM.RRRR</string>
    <string id="settings_weekday_yyyy_mm_dd" scope="settings">DZIEŃ TYGODNIA, RRRR-MM-DD</string>
    <string id="settings_weekday_mm_dd_yyyy" scope="settings">DZIEŃ TYGODNIA, MM/DD/RRRR</string>
    <string id="settings_weekday_dd_mm_yyyy" scope="settings">DZIEŃ TYGODNIA, DD.MM.RRRR</string>
    <string id="settings_weekday_dd_month_week" scope="settings">DZIEŃ TYGODNIA, DD MIESIĄC (Tydzień)</string>
    <string id="settings_weekday_dd_month_yyyy_week" scope="settings">DZIEŃ TYGODNIA, DD MIESIĄC RRRR (Tydzień)</string>
    <string id="settings_alternative_timezone_1_label" scope="settings">Etykieta alternatywnej strefy czasowej 1</string>
    <string id="settings_alternative_timezone_1_offset" scope="settings">Przesunięcie alternatywnej strefy czasowej 1</string>
    <string id="settings_alternative_timezone_2_label" scope="settings">Etykieta alternatywnej strefy czasowej 2</string>
    <string id="settings_alternative_timezone_2_offset" scope="settings">Przesunięcie alternatywnej strefy czasowej 2</string>
    <string id="settings_temperature_unit" scope="settings">Jednostka temperatury</string>
    <string id="settings_c" scope="settings">C</string>
    <string id="settings_f" scope="settings">F</string>
    <string id="settings_wind_speed_unit" scope="settings">Jednostka prędkości wiatru</string>
    <string id="settings_ms" scope="settings">m/s</string>
    <string id="settings_kmh" scope="settings">km/h</string>
    <string id="settings_mph" scope="settings">mph</string>
    <string id="settings_knots" scope="settings">węzły</string>
    <string id="settings_beufort" scope="settings">beaufort</string>
    <string id="settings_pressure_unit" scope="settings">Jednostka ciśnienia</string>
    <string id="settings_hpa" scope="settings">hPa</string>
    <string id="settings_mmhg" scope="settings">mmHg</string>
    <string id="settings_inhg" scope="settings">inHg</string>
    <string id="settings_week_number_offset" scope="settings">Przesunięcie numeru tygodnia (0 = normalny numer tygodnia)</string>
    <string id="settings_show_clock_background" scope="settings">Pokaż tło zegara</string>
    <string id="settings_show_data_fields_background" scope="settings">Pokaż tło pól danych</string>
    <string id="settings_show_notification_count" scope="settings">Pokaż liczbę powiadomień</string>
    <string id="settings_zeropad_hour_clock" scope="settings">Dodaj zero do godziny na zegarze</string>
    <string id="settings_separator_hours_minutes" scope="settings">Separator między godzinami a minutami</string>
    <string id="settings_colon" scope="settings">:</string>
    <string id="settings_blank_space" scope="settings">Pusta przestrzeń</string>
    <string id="settings_no_separator" scope="settings">Brak separatora</string>
    <string id="settings_show_seconds_active" scope="settings">Pokaż sekundy w trybie aktywnym</string>
    <string id="settings_show_seconds_inactive" scope="settings">Pokaż sekundy w trybie nieaktywnym (tylko ekrany MIP)</string>
    <string id="settings_show_stress_body_battery" scope="settings">Pokaż paski stresu (pomarańczowy) i baterii ciała (niebieski)</string>
    <string id="settings_show_hide_labels" scope="settings">Pokaż/Ukryj etykiety</string>
    <string id="settings_show_all_labels" scope="settings">Pokaż wszystkie etykiety</string>
    <string id="settings_hide_all_labels" scope="settings">Ukryj wszystkie etykiety</string>
    <string id="settings_hide_top_labels" scope="settings">Ukryj górne etykiety</string>
    <string id="settings_hide_bottom_labels" scope="settings">Ukryj dolne etykiety</string>
</strings>