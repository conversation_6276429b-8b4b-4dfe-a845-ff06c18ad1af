<?xml version="1.0"?>
<!-- This is a generated file. It is highly recommended that you DO NOT edit this file. -->
<iq:manifest version="3" xmlns:iq="http://www.garmin.com/xml/connectiq">
    <!--
        Use "Monkey C: Edit Application" from the Visual Studio Code command palette
        to update the application attributes.
    -->
    <iq:application id="21db93a7-4639-498f-844b-28967a15eae9" type="watchface" name="@Strings.AppName" entry="Segment34App" launcherIcon="@Drawables.LauncherIcon" minApiLevel="3.2.0">
        <!--
            Use the following from the Visual Studio Code comand palette to edit
            the build targets:
            "Monkey C: Set Products by Product Category" - Lets you add all products
                                       that belong to the same product category
            "Monkey C: Edit Products" - Lets you add or remove any product
        -->
        <iq:products>
            <iq:product id="approachs50"/>
            <iq:product id="approachs7042mm"/>
            <iq:product id="approachs7047mm"/>
            <iq:product id="d2airx10"/>
            <iq:product id="d2mach1"/>
            <iq:product id="descentg2"/>
            <iq:product id="descentmk2"/>
            <iq:product id="descentmk2s"/>
            <iq:product id="descentmk343mm"/>
            <iq:product id="descentmk351mm"/>
            <iq:product id="enduro"/>
            <iq:product id="enduro3"/>
            <iq:product id="epix2"/>
            <iq:product id="epix2pro42mm"/>
            <iq:product id="epix2pro47mm"/>
            <iq:product id="epix2pro51mm"/>
            <iq:product id="fenix6"/>
            <iq:product id="fenix6pro"/>
            <iq:product id="fenix6s"/>
            <iq:product id="fenix6spro"/>
            <iq:product id="fenix6xpro"/>
            <iq:product id="fenix7"/>
            <iq:product id="fenix7pro"/>
            <iq:product id="fenix7pronowifi"/>
            <iq:product id="fenix7s"/>
            <iq:product id="fenix7spro"/>
            <iq:product id="fenix7x"/>
            <iq:product id="fenix7xpro"/>
            <iq:product id="fenix7xpronowifi"/>
            <iq:product id="fenix843mm"/>
            <iq:product id="fenix847mm"/>
            <iq:product id="fenix8pro47mm"/>
            <iq:product id="fenix8solar47mm"/>
            <iq:product id="fenix8solar51mm"/>
            <iq:product id="fenixe"/>
            <iq:product id="fr165"/>
            <iq:product id="fr165m"/>
            <iq:product id="fr245"/>
            <iq:product id="fr245m"/>
            <iq:product id="fr255"/>
            <iq:product id="fr255m"/>
            <iq:product id="fr265"/>
            <iq:product id="fr265s"/>
            <iq:product id="fr57042mm"/>
            <iq:product id="fr57047mm"/>
            <iq:product id="fr745"/>
            <iq:product id="fr945"/>
            <iq:product id="fr945lte"/>
            <iq:product id="fr955"/>
            <iq:product id="fr965"/>
            <iq:product id="fr970"/>
            <iq:product id="instinct3amoled45mm"/>
            <iq:product id="instinct3amoled50mm"/>
            <iq:product id="legacyherofirstavenger"/>
            <iq:product id="legacysagadarthvader"/>
            <iq:product id="marq2"/>
            <iq:product id="marq2aviator"/>
            <iq:product id="marqadventurer"/>
            <iq:product id="marqathlete"/>
            <iq:product id="marqaviator"/>
            <iq:product id="marqcaptain"/>
            <iq:product id="marqcommander"/>
            <iq:product id="marqdriver"/>
            <iq:product id="marqexpedition"/>
            <iq:product id="marqgolfer"/>
            <iq:product id="venu"/>
            <iq:product id="venu2"/>
            <iq:product id="venu2plus"/>
            <iq:product id="venu2s"/>
            <iq:product id="venu3"/>
            <iq:product id="venu3s"/>
            <iq:product id="venux1"/>
            <iq:product id="vivoactive4"/>
            <iq:product id="vivoactive5"/>
            <iq:product id="vivoactive6"/>
        </iq:products>
        <!--
            Use "Monkey C: Edit Permissions" from the Visual Studio Code command
            palette to update permissions.
        -->
        <iq:permissions>
            <iq:uses-permission id="ComplicationSubscriber"/>
            <iq:uses-permission id="Positioning"/>
            <iq:uses-permission id="SensorHistory"/>
            <iq:uses-permission id="UserProfile"/>
        </iq:permissions>
        <!--
            Use "Monkey C: Edit Languages" from the Visual Studio Code command
            palette to edit your compatible language list.
        -->
        <iq:languages>
            <iq:language>eng</iq:language>
            <iq:language>fre</iq:language>
            <iq:language>ita</iq:language>
            <iq:language>pol</iq:language>
        </iq:languages>
        <!--
            Use "Monkey C: Configure Monkey Barrel" from the Visual Studio Code
            command palette to edit the included barrels.
        -->
        <iq:barrels/>
    </iq:application>
</iq:manifest>